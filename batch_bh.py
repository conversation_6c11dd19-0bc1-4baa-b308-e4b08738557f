import logging
import time
import uuid
import secrets
import hashlib
import base64
import requests
import json
import os
from datetime import datetime
from typing import Optional, Tuple, Dict, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_optimized_chrome_driver(headless: bool = False):
    """
    创建优化的Chrome WebDriver，解决Bad Gateway错误

    Args:
        headless: 是否使用无头模式

    Returns:
        WebDriver实例
    """
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service

    # 解决代理干扰问题
    os.environ['NO_PROXY'] = 'localhost,127.0.0.1,::1'

    # 路径配置
    chrome_binary_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe"
    chromedriver_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chromedriver.exe"

    # Chrome选项配置
    options = Options()

    # 设置Chrome二进制路径（如果存在）
    if os.path.exists(chrome_binary_path):
        options.binary_location = chrome_binary_path
        logging.info(f"使用Chrome路径: {chrome_binary_path}")

    # 代理绕过配置
    options.add_argument('--proxy-bypass-list=localhost,127.0.0.1,::1')

    # 基础配置
    if headless:
        options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-web-security')
    options.add_argument('--allow-running-insecure-content')
    options.add_argument('--ignore-certificate-errors')
    options.add_argument('--ignore-ssl-errors')
    options.add_argument('--ignore-certificate-errors-spki-list')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--remote-debugging-port=0')
    options.add_argument('--no-first-run')
    options.add_argument('--no-default-browser-check')

    # 尝试创建WebDriver
    try:
        # 方案1: 使用指定的ChromeDriver路径
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=options)
            logging.info("✅ 使用指定ChromeDriver路径成功创建WebDriver")
            return driver
    except Exception as e:
        logging.warning(f"使用指定ChromeDriver路径失败: {e}")

    try:
        # 方案2: 使用ChromeDriverManager
        from webdriver_manager.chrome import ChromeDriverManager
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        logging.info("✅ 使用ChromeDriverManager成功创建WebDriver")
        return driver
    except ImportError:
        logging.warning("webdriver_manager未安装")
    except Exception as e:
        logging.warning(f"ChromeDriverManager失败: {e}")

    try:
        # 方案3: 使用系统PATH中的chromedriver
        driver = webdriver.Chrome(options=options)
        logging.info("✅ 使用系统chromedriver成功创建WebDriver")
        return driver
    except Exception as e:
        logging.error(f"所有方案都失败了: {e}")
        raise Exception("无法创建Chrome WebDriver，请检查Chrome和ChromeDriver安装")

def get_cursor_session_token(driver, max_attempts: int = 3, retry_interval: int = 2, cookies: Dict[str, str] = None) -> Optional[Tuple[str, str]]:
    """
    获取Cursor会话token (从bh.py复制的核心逻辑)
    
    Args:
        driver: Selenium WebDriver对象
        max_attempts: 最大尝试次数
        retry_interval: 重试间隔(秒)
        cookies: 要设置的cookies字典，格式为{name: value}
        
    Returns:
        Tuple[str, str] | None: 成功返回(userId, accessToken)元组，失败返回None
    """
    logging.info("开始获取会话令牌")
    
    def _generate_pkce_pair():
        """生成PKCE验证对"""
        code_verifier = secrets.token_urlsafe(43)
        code_challenge_digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(code_challenge_digest).decode('utf-8').rstrip('=')    
        return code_verifier, code_challenge
    
    attempts = 0
    while attempts < max_attempts:
        try:
            verifier, challenge = _generate_pkce_pair()
            id = uuid.uuid4()
            client_login_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={id}&mode=login"
            
            logging.info(f"首先访问网站: https://www.cursor.com/")
            driver.get("https://www.cursor.com/")
            time.sleep(1)
            
            if cookies:
                logging.info("设置浏览器cookies")
                for name, value in cookies.items():
                    driver.add_cookie({"name": name, "value": value, "path": "/"})
                logging.info("cookies设置完成")
            
            logging.info(f"访问深度登录URL: {client_login_url}")
            driver.get(client_login_url)
            
            try:
                login_button = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Yes, Log In')]"))
                )
                logging.info("点击确认登录按钮")
                login_button.click()
                time.sleep(1.5)
                
                auth_poll_url = f"https://api2.cursor.sh/auth/poll?uuid={id}&verifier={verifier}"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36",
                    "Accept": "*/*"
                }
                
                logging.info(f"轮询认证状态: {auth_poll_url}")
                response = requests.get(auth_poll_url, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    accessToken = data.get("accessToken", None)
                    authId = data.get("authId", "")
                    
                    if accessToken:
                        userId = ""
                        if len(authId.split("|")) > 1:
                            userId = authId.split("|")[1]
                        
                        logging.info("成功获取账号token和userId")
                        return userId, accessToken
                else:
                    logging.error(f"API请求失败，状态码: {response.status_code}")
            except Exception as e:
                logging.warning(f"未找到登录确认按钮或点击失败: {str(e)}")
                
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts
                logging.warning(f"第 {attempts} 次尝试未获取到token，{wait_time}秒后重试...")
                time.sleep(wait_time)
        except Exception as e:
            logging.error(f"深度登录获取token失败: {str(e)}")
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts
                logging.warning(f"将在 {wait_time} 秒后重试...")
                time.sleep(wait_time)
    
    logging.error(f"在 {max_attempts} 次尝试后仍未获取到token")
    return None

def init_browser(headless: bool = False):
    """
    初始化浏览器 (优化版本，解决Bad Gateway错误)
    """
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    import os

    # 解决代理干扰问题 - 配置环境变量绕过本地地址
    os.environ['NO_PROXY'] = 'localhost,127.0.0.1,::1'

    # 临时禁用可能干扰的代理环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    original_proxy_values = {}
    for var in proxy_vars:
        if var in os.environ:
            original_proxy_values[var] = os.environ[var]
            os.environ.pop(var, None)

    chrome_options = Options()

    # 自动检测Chrome路径
    possible_chrome_paths = [
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe",
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
    ]

    chrome_path = None
    for path in possible_chrome_paths:
        if os.path.exists(path):
            chrome_path = path
            logging.info(f"找到Chrome路径: {chrome_path}")
            break

    # 只有找到Chrome路径时才设置binary_location
    if chrome_path:
        chrome_options.binary_location = chrome_path
    else:
        logging.info("未找到指定Chrome路径，使用系统默认Chrome")

    if headless:
        chrome_options.add_argument("--headless")

    # Chrome选项配置 - 添加代理绕过配置
    chrome_options.add_argument("--proxy-bypass-list=localhost,127.0.0.1,::1")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--ignore-certificate-errors")
    chrome_options.add_argument("--ignore-ssl-errors")
    chrome_options.add_argument("--ignore-certificate-errors-spki-list")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--remote-debugging-port=0")  # 避免端口冲突
    chrome_options.add_argument("--no-first-run")
    chrome_options.add_argument("--no-default-browser-check")

    logging.info("初始化Chrome浏览器" + (" (无头模式)" if headless else ""))

    # 自动检测ChromeDriver路径
    possible_chromedriver_paths = [
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chromedriver.exe",
        "chromedriver.exe",  # 当前目录
        "chromedriver"       # 系统PATH
    ]

    chromedriver_path = None
    for path in possible_chromedriver_paths:
        if os.path.exists(path):
            chromedriver_path = path
            logging.info(f"找到ChromeDriver路径: {chromedriver_path}")
            break

    driver = None

    try:
        # 方案1: 使用指定的ChromeDriver路径
        if chromedriver_path:
            try:
                service = Service(chromedriver_path, port=9516)  # 使用不同端口避免冲突
                driver = webdriver.Chrome(service=service, options=chrome_options)
                logging.info("✅ 使用指定ChromeDriver路径成功初始化浏览器")
            except Exception as e:
                logging.warning(f"使用指定ChromeDriver路径失败: {e}")

        # 方案2: 使用ChromeDriverManager自动管理驱动
        if not driver:
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install(), port=9517)
                driver = webdriver.Chrome(service=service, options=chrome_options)
                logging.info("✅ 使用ChromeDriverManager成功初始化浏览器")
            except ImportError:
                logging.warning("webdriver_manager未安装")
            except Exception as e:
                logging.warning(f"ChromeDriverManager失败: {e}")

        # 方案3: 使用系统PATH中的chromedriver
        if not driver:
            try:
                driver = webdriver.Chrome(options=chrome_options)
                logging.info("✅ 使用系统chromedriver成功初始化浏览器")
            except Exception as e:
                logging.warning(f"系统chromedriver失败: {e}")

        if driver:
            # 恢复原始代理环境变量
            for var, value in original_proxy_values.items():
                os.environ[var] = value
            return driver

    except Exception as e:
        logging.error(f"Chrome浏览器初始化失败: {e}")

        # 最后的回退方案：使用简化配置
        if chrome_path:
            logging.info("尝试使用简化配置...")
            chrome_options_fallback = Options()
            chrome_options_fallback.add_argument("--proxy-bypass-list=localhost,127.0.0.1,::1")
            if headless:
                chrome_options_fallback.add_argument("--headless")
            chrome_options_fallback.add_argument("--disable-gpu")
            chrome_options_fallback.add_argument("--no-sandbox")
            chrome_options_fallback.add_argument("--disable-dev-shm-usage")

            try:
                if chromedriver_path:
                    service = Service(chromedriver_path, port=9518)
                    driver = webdriver.Chrome(service=service, options=chrome_options_fallback)
                else:
                    driver = webdriver.Chrome(options=chrome_options_fallback)
                logging.info("✅ 使用简化配置成功初始化浏览器")
                # 恢复原始代理环境变量
                for var, value in original_proxy_values.items():
                    os.environ[var] = value
                return driver
            except Exception as e2:
                logging.error(f"简化配置也失败: {e2}")

        # 恢复原始代理环境变量
        for var, value in original_proxy_values.items():
            os.environ[var] = value

        logging.error("Chrome浏览器初始化失败，请检查:")
        logging.error("1. Chrome是否正确安装")
        logging.error("2. ChromeDriver是否与Chrome版本匹配")
        logging.error("3. 是否有代理软件干扰（如Clash、V2Ray等）")
        logging.error("4. 运行: python install_dependencies.py")
        logging.error("5. 手动安装: pip install webdriver-manager")
        raise

def load_workos_tokens(file_path: str = "workos_tokens.json") -> List[Dict]:
    """
    加载WorkosCursorSessionToken数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logging.error(f"读取{file_path}失败: {e}")
        return []

def save_batch_results(results: List[Dict], output_file: str = "batch_bh_results.json"):
    """
    保存批量保号结果
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=4)
        logging.info(f"批量结果已保存到 {output_file}")
    except Exception as e:
        logging.error(f"保存结果文件失败: {e}")

def save_new_login_data(new_login_data: List[Dict], output_file: str = "batch_login_data.json"):
    """
    保存新获取的登录数据到独立文件
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(new_login_data, f, ensure_ascii=False, indent=4)
        logging.info(f"新登录数据已保存到 {output_file}")
        return True
    except Exception as e:
        logging.error(f"保存新登录数据失败: {e}")
        return False

def batch_keep_alive():
    """
    批量保号主函数
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('batch_bh.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 加载所有token
    tokens_data = load_workos_tokens()
    if not tokens_data:
        logging.error("没有找到可处理的token数据")
        return
    
    logging.info(f"加载了 {len(tokens_data)} 个token，开始批量保号")

    # 初始化浏览器 - 使用优化版本
    try:
        driver = create_optimized_chrome_driver(headless=False)
        logging.info("浏览器初始化成功，开始批量处理...")
    except Exception as e:
        logging.error(f"浏览器初始化失败: {e}")
        logging.error("尝试使用备用初始化方法...")
        try:
            driver = init_browser(headless=False)
        except Exception as e2:
            logging.error(f"备用初始化方法也失败: {e2}")
            return
    
    results = []
    new_login_data = []  # 存储新获取的登录数据
    success_count = 0
    failure_count = 0
    
    try:
        for index, token_data in enumerate(tokens_data, 1):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            session_token = token_data.get('WorkosCursorSessionToken')
            original_timestamp = token_data.get('timestamp', '')
            
            logging.info(f"\n{'='*50}")
            logging.info(f"处理第 {index}/{len(tokens_data)} 个token")
            logging.info(f"原始时间戳: {original_timestamp}")
            logging.info(f"开始时间: {current_time}")
            
            if not session_token:
                logging.error(f"第 {index} 个条目缺少WorkosCursorSessionToken")
                results.append({
                    "index": index,
                    "original_timestamp": original_timestamp,
                    "process_time": current_time,
                    "status": "失败",
                    "error": "缺少WorkosCursorSessionToken",
                    "new_userId": None,
                    "new_accessToken": None
                })
                failure_count += 1
                continue
            
            # 设置cookies并尝试保号
            cookies = {"WorkosCursorSessionToken": session_token}
            
            try:
                result = get_cursor_session_token(driver, cookies=cookies)
                
                if result:
                    userId, accessToken = result
                    logging.info(f"✅ 第 {index} 个token保号成功!")
                    logging.info(f"新 userId: {userId}")
                    
                    results.append({
                        "index": index,
                        "original_timestamp": original_timestamp,
                        "process_time": current_time,
                        "status": "成功",
                        "error": None,
                        "new_userId": userId,
                        "new_accessToken": accessToken,
                        "original_WorkosCursorSessionToken": session_token
                    })
                    success_count += 1
                    
                    # 添加到新登录数据列表（不追加到原文件）
                    new_entry = {
                        "timestamp": f"{current_time}（批量保号更新 - {original_timestamp}）",
                        "userId": userId,
                        "accessToken": accessToken
                    }
                    new_login_data.append(new_entry)
                        
                else:
                    logging.error(f"❌ 第 {index} 个token保号失败")
                    results.append({
                        "index": index,
                        "original_timestamp": original_timestamp,
                        "process_time": current_time,
                        "status": "失败",
                        "error": "无法获取新token",
                        "new_userId": None,
                        "new_accessToken": None,
                        "original_WorkosCursorSessionToken": session_token
                    })
                    failure_count += 1
                    
            except Exception as e:
                logging.error(f"❌ 第 {index} 个token处理异常: {str(e)}")
                results.append({
                    "index": index,
                    "original_timestamp": original_timestamp,
                    "process_time": current_time,
                    "status": "失败",
                    "error": str(e),
                    "new_userId": None,
                    "new_accessToken": None,
                    "original_WorkosCursorSessionToken": session_token
                })
                failure_count += 1
            
            # 每处理完一个token后等待一段时间，避免请求过于频繁
            if index < len(tokens_data):
                wait_time = 3
                logging.info(f"等待 {wait_time} 秒后处理下一个token...")
                time.sleep(wait_time)
                
    finally:
        driver.quit()
        
        # 保存结果
        save_batch_results(results)
        
        # 保存新获取的登录数据到独立文件
        if new_login_data:
            save_new_login_data(new_login_data)
            logging.info(f"成功保存 {len(new_login_data)} 条新登录数据")
        
        # 打印统计信息
        logging.info(f"\n{'='*50}")
        logging.info("批量保号完成!")
        logging.info(f"总共处理: {len(tokens_data)} 个token")
        logging.info(f"成功: {success_count} 个")
        logging.info(f"失败: {failure_count} 个")
        logging.info(f"成功率: {(success_count/len(tokens_data)*100):.2f}%")
        logging.info(f"详细结果已保存到: batch_bh_results.json")
        logging.info(f"新登录数据已保存到: batch_login_data.json")
        logging.info(f"日志文件: batch_bh.log")

def test_browser_initialization():
    """
    测试浏览器初始化功能
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    logging.info("开始测试浏览器初始化...")

    try:
        # 测试优化版本
        driver = create_optimized_chrome_driver(headless=False)
        logging.info("✅ 优化版本浏览器初始化成功")

        # 简单测试
        driver.get("https://www.baidu.com")
        logging.info(f"✅ 页面访问成功，标题: {driver.title}")

        driver.quit()
        logging.info("✅ 浏览器测试完成")

    except Exception as e:
        logging.error(f"❌ 优化版本失败: {e}")

        try:
            # 测试备用版本
            logging.info("尝试备用初始化方法...")
            driver = init_browser(headless=False)
            logging.info("✅ 备用版本浏览器初始化成功")

            driver.get("https://www.baidu.com")
            logging.info(f"✅ 页面访问成功，标题: {driver.title}")

            driver.quit()
            logging.info("✅ 备用版本测试完成")

        except Exception as e2:
            logging.error(f"❌ 备用版本也失败: {e2}")
            logging.error("请检查Chrome和ChromeDriver安装")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_browser_initialization()
    else:
        batch_keep_alive()