#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个保号脚本 - 使用config.json配置文件
从config.json读取session_token进行单个账号保号
"""

import logging
import time
import uuid
import secrets
import hashlib
import base64
import requests
import json
import os
from datetime import datetime
from typing import Optional, Tuple, Dict
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_optimized_chrome_driver(headless: bool = False):
    """
    创建优化的Chrome WebDriver，解决Bad Gateway错误
    
    Args:
        headless: 是否使用无头模式
        
    Returns:
        WebDriver实例
    """
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    
    # 解决代理干扰问题
    os.environ['NO_PROXY'] = 'localhost,127.0.0.1,::1'
    
    # 路径配置
    chrome_binary_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe"
    chromedriver_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chromedriver.exe"
    
    # Chrome选项配置
    options = Options()
    
    # 设置Chrome二进制路径（如果存在）
    if os.path.exists(chrome_binary_path):
        options.binary_location = chrome_binary_path
        logging.info(f"使用Chrome路径: {chrome_binary_path}")
    
    # 代理绕过配置
    options.add_argument('--proxy-bypass-list=localhost,127.0.0.1,::1')
    
    # 基础配置
    if headless:
        options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-web-security')
    options.add_argument('--allow-running-insecure-content')
    options.add_argument('--ignore-certificate-errors')
    options.add_argument('--ignore-ssl-errors')
    options.add_argument('--ignore-certificate-errors-spki-list')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--remote-debugging-port=0')
    options.add_argument('--no-first-run')
    options.add_argument('--no-default-browser-check')
    
    # 尝试创建WebDriver
    try:
        # 方案1: 使用指定的ChromeDriver路径
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=options)
            logging.info("✅ 使用指定ChromeDriver路径成功创建WebDriver")
            return driver
    except Exception as e:
        logging.warning(f"使用指定ChromeDriver路径失败: {e}")
    
    try:
        # 方案2: 使用ChromeDriverManager
        from webdriver_manager.chrome import ChromeDriverManager
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        logging.info("✅ 使用ChromeDriverManager成功创建WebDriver")
        return driver
    except ImportError:
        logging.warning("webdriver_manager未安装")
    except Exception as e:
        logging.warning(f"ChromeDriverManager失败: {e}")
    
    try:
        # 方案3: 使用系统PATH中的chromedriver
        driver = webdriver.Chrome(options=options)
        logging.info("✅ 使用系统chromedriver成功创建WebDriver")
        return driver
    except Exception as e:
        logging.error(f"所有方案都失败了: {e}")
        raise Exception("无法创建Chrome WebDriver，请检查Chrome和ChromeDriver安装")

def get_cursor_session_token(driver, session_token: str, max_attempts: int = 3, retry_interval: int = 2) -> Optional[Tuple[str, str]]:
    """
    获取Cursor会话token
    
    Args:
        driver: Selenium WebDriver对象
        session_token: WorkosCursorSessionToken
        max_attempts: 最大尝试次数
        retry_interval: 重试间隔(秒)
        
    Returns:
        Tuple[str, str] | None: 成功返回(userId, accessToken)元组，失败返回None
    """
    logging.info("开始获取会话令牌")
    
    def _generate_pkce_pair():
        """生成PKCE验证对"""
        code_verifier = secrets.token_urlsafe(43)
        code_challenge_digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(code_challenge_digest).decode('utf-8').rstrip('=')    
        return code_verifier, code_challenge
    
    attempts = 0
    while attempts < max_attempts:
        try:
            verifier, challenge = _generate_pkce_pair()
            id = uuid.uuid4()
            client_login_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={id}&mode=login"
            
            logging.info(f"首先访问网站: https://www.cursor.com/")
            driver.get("https://www.cursor.com/")
            time.sleep(1)
            
            logging.info("设置浏览器cookies")
            cookies = {"WorkosCursorSessionToken": session_token}
            for name, value in cookies.items():
                driver.add_cookie({"name": name, "value": value, "path": "/"})
            logging.info("cookies设置完成")
            
            logging.info(f"访问深度登录URL: {client_login_url}")
            driver.get(client_login_url)
            
            try:
                login_button = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Yes, Log In')]"))
                )
                logging.info("点击确认登录按钮")
                login_button.click()
                time.sleep(1.5)
                
                auth_poll_url = f"https://api2.cursor.sh/auth/poll?uuid={id}&verifier={verifier}"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36",
                    "Accept": "*/*"
                }
                
                logging.info(f"轮询认证状态: {auth_poll_url}")
                response = requests.get(auth_poll_url, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    accessToken = data.get("accessToken", None)
                    authId = data.get("authId", "")
                    
                    if accessToken:
                        userId = ""
                        if len(authId.split("|")) > 1:
                            userId = authId.split("|")[1]
                        
                        logging.info("成功获取账号token和userId")
                        return userId, accessToken
                else:
                    logging.error(f"API请求失败，状态码: {response.status_code}")
            except Exception as e:
                logging.warning(f"未找到登录确认按钮或点击失败: {str(e)}")
                
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts
                logging.warning(f"第 {attempts} 次尝试未获取到token，{wait_time}秒后重试...")
                time.sleep(wait_time)
        except Exception as e:
            logging.error(f"深度登录获取token失败: {str(e)}")
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts
                logging.warning(f"将在 {wait_time} 秒后重试...")
                time.sleep(wait_time)
    
    logging.error(f"在 {max_attempts} 次尝试后仍未获取到token")
    return None

def load_config(config_file: str = "config.json") -> Dict:
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logging.info(f"成功加载配置文件: {config_file}")
        return config
    except FileNotFoundError:
        logging.error(f"配置文件不存在: {config_file}")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"配置文件格式错误: {e}")
        return {}
    except Exception as e:
        logging.error(f"读取配置文件失败: {e}")
        return {}

def add_original_token_field(result: Dict) -> Dict:
    """
    为结果添加original_WorkosCursorSessionToken字段

    Args:
        result: 结果字典

    Returns:
        添加字段后的结果字典
    """
    if result.get('status') == '成功' and result.get('new_userId') and result.get('new_accessToken'):
        user_id = result['new_userId']
        access_token = result['new_accessToken']
        # 构造 original_WorkosCursorSessionToken
        # 格式：new_userId + %3A%3A + new_accessToken
        original_token = f"{user_id}%3A%3A{access_token}"
        result['original_WorkosCursorSessionToken'] = original_token
        logging.info(f"为用户 {user_id} 添加了 original_WorkosCursorSessionToken")

    return result

def save_result(result: Dict, output_file: str = "single_bh_result.json"):
    """
    保存保号结果（追加模式）

    Args:
        result: 结果字典
        output_file: 输出文件路径
    """
    try:
        # 添加original_WorkosCursorSessionToken字段
        result = add_original_token_field(result)

        # 读取现有数据
        existing_data = []
        if os.path.exists(output_file):
            try:
                with open(output_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    # 确保现有数据是列表格式
                    if not isinstance(existing_data, list):
                        existing_data = [existing_data]
            except (json.JSONDecodeError, Exception) as e:
                logging.warning(f"读取现有结果文件失败，将创建新文件: {e}")
                existing_data = []

        # 追加新结果
        existing_data.append(result)

        # 保存更新后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=4)

        logging.info(f"结果已追加保存到: {output_file} (共 {len(existing_data)} 条记录)")
    except Exception as e:
        logging.error(f"保存结果失败: {e}")

def single_keep_alive():
    """
    单个保号主函数
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('single_bh.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logging.info(f"开始单个保号任务 - {current_time}")
    
    # 加载配置
    config = load_config()
    if not config:
        logging.error("无法加载配置文件，程序退出")
        return
    
    session_token = config.get('session_token')
    if not session_token:
        logging.error("配置文件中缺少session_token，程序退出")
        return
    
    logging.info(f"从配置文件读取到session_token: {session_token[:50]}...")
    
    # 初始化浏览器
    try:
        driver = create_optimized_chrome_driver(headless=False)
        logging.info("浏览器初始化成功")
    except Exception as e:
        logging.error(f"浏览器初始化失败: {e}")
        return
    
    result = {
        "timestamp": current_time,
        "input_session_token": session_token,
        "status": "失败",
        "error": None,
        "new_userId": None,
        "new_accessToken": None,
        "original_WorkosCursorSessionToken": None
    }
    
    try:
        # 执行保号
        token_result = get_cursor_session_token(driver, session_token)
        
        if token_result:
            userId, accessToken = token_result
            logging.info("✅ 保号成功!")
            logging.info(f"新 userId: {userId}")
            logging.info(f"新 accessToken: {accessToken[:50]}...")
            
            result.update({
                "status": "成功",
                "new_userId": userId,
                "new_accessToken": accessToken
            })
        else:
            logging.error("❌ 保号失败")
            result["error"] = "无法获取新token"
            
    except Exception as e:
        logging.error(f"❌ 保号过程异常: {str(e)}")
        result["error"] = str(e)
        
    finally:
        driver.quit()
        
        # 保存结果
        save_result(result)
        
        # 打印总结
        logging.info(f"\n{'='*50}")
        logging.info("单个保号任务完成!")
        logging.info(f"状态: {result['status']}")
        if result['status'] == '成功':
            logging.info(f"新 userId: {result['new_userId']}")
            logging.info("✅ 保号成功")
        else:
            logging.info(f"错误: {result['error']}")
            logging.info("❌ 保号失败")
        logging.info(f"详细结果已保存到: single_bh_result.json")
        logging.info(f"日志文件: single_bh.log")

if __name__ == "__main__":
    single_keep_alive()
