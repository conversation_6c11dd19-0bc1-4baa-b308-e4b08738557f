#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：为single_bh_result.json添加missing的original_WorkosCursorSessionToken字段
作者：Claude
"""

import json
import re
from typing import List, Dict, Any

def extract_userid_from_input_token(input_session_token: str) -> str:
    """从input_session_token中提取userId"""
    if '%3A%3A' in input_session_token:
        return input_session_token.split('%3A%3A')[0]
    return ""

def process_single_bh_result(input_file: str, output_file: str) -> None:
    """
    处理single_bh_result.json文件，添加missing的original_WorkosCursorSessionToken字段
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    try:
        # 读取原文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 处理每个记录
        for record in data:
            if 'new_userId' in record and 'new_accessToken' in record:
                # 构造 original_WorkosCursorSessionToken
                # 格式：new_userId + %3A%3A + new_accessToken
                user_id = record['new_userId']
                access_token = record['new_accessToken']
                
                # 构造新的token
                original_token = f"{user_id}%3A%3A{access_token}"
                record['original_WorkosCursorSessionToken'] = original_token
                
                print(f"✅ 为用户 {user_id} 添加了 original_WorkosCursorSessionToken")
        
        # 写入新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        print(f"✅ 处理完成！已将结果保存到: {output_file}")
        print(f"📊 共处理了 {len(data)} 条记录")
        
    except FileNotFoundError:
        print(f"❌ 错误：找不到文件 {input_file}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")

def main():
    """主函数"""
    input_file = "single_bh_result.json"
    output_file = "single_bh_result_processed.json"
    
    print("🚀 开始处理single_bh_result.json文件...")
    print(f"📂 输入文件: {input_file}")
    print(f"📂 输出文件: {output_file}")
    print("-" * 50)
    
    process_single_bh_result(input_file, output_file)

if __name__ == "__main__":
    main()