2025-07-16 10:54:41,101 - INFO - 开始单个保号任务 - 2025-07-16 10:54:41
2025-07-16 10:54:41,102 - INFO - 成功加载配置文件: config.json
2025-07-16 10:54:41,102 - INFO - 从配置文件读取到session_token: user_01JXQ2R40Y775XD4PQ7CAE7MKH%3A%3AeyJhbGciOiJIU...
2025-07-16 10:54:41,102 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 10:54:43,298 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 10:54:43,298 - INFO - 浏览器初始化成功
2025-07-16 10:54:43,299 - INFO - 开始获取会话令牌
2025-07-16 10:54:43,299 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 10:54:54,700 - INFO - 设置浏览器cookies
2025-07-16 10:54:55,061 - INFO - cookies设置完成
2025-07-16 10:54:55,063 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=WqyUSIJMzv4c3ilIDGG6leg_3vSatS1TRkRNJ3mkeQg&uuid=46573da3-c484-43ee-a0d0-95e9c6a621fa&mode=login
2025-07-16 10:55:01,610 - WARNING - 未找到登录确认按钮或点击失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff7701be925+77845]
	GetHandleVerifier [0x0x7ff7701be980+77936]
	(No symbol) [0x0x7ff76ff79cda]
	(No symbol) [0x0x7ff76ffd06aa]
	(No symbol) [0x0x7ff76ffd095c]
	(No symbol) [0x0x7ff770023d07]
	(No symbol) [0x0x7ff76fff890f]
	(No symbol) [0x0x7ff770020b07]
	(No symbol) [0x0x7ff76fff86a3]
	(No symbol) [0x0x7ff76ffc1791]
	(No symbol) [0x0x7ff76ffc2523]
	GetHandleVerifier [0x0x7ff77049683d+3059501]
	GetHandleVerifier [0x0x7ff770490bfd+3035885]
	GetHandleVerifier [0x0x7ff7704b03f0+3164896]
	GetHandleVerifier [0x0x7ff7701d8c2e+185118]
	GetHandleVerifier [0x0x7ff7701e053f+216111]
	GetHandleVerifier [0x0x7ff7701c72d4+113092]
	GetHandleVerifier [0x0x7ff7701c7489+113529]
	GetHandleVerifier [0x0x7ff7701ae288+10616]
	BaseThreadInitThunk [0x0x7ffeced17374+20]
	RtlUserThreadStart [0x0x7ffecee5cc91+33]

2025-07-16 10:55:01,611 - WARNING - 第 1 次尝试未获取到token，2秒后重试...
2025-07-16 10:55:03,612 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 10:55:05,929 - INFO - 设置浏览器cookies
2025-07-16 10:55:08,236 - INFO - cookies设置完成
2025-07-16 10:55:08,237 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=mNru9XwcvgldpWeHkExiTTrjfsPG2EyGcZrUh4ByFTE&uuid=5f847480-a631-43f2-a097-af0cbcb2ab24&mode=login
2025-07-16 10:55:11,047 - WARNING - 未找到登录确认按钮或点击失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-16 10:55:11,048 - WARNING - 第 2 次尝试未获取到token，4秒后重试...
2025-07-16 10:55:15,049 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 10:55:19,143 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B3567ED0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/97dde21aed484580639fc95ad0b2aa98/url
2025-07-16 10:55:23,213 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B36342D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/97dde21aed484580639fc95ad0b2aa98/url
2025-07-16 10:55:27,314 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B355EFD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/97dde21aed484580639fc95ad0b2aa98/url
2025-07-16 10:55:31,385 - ERROR - 深度登录获取token失败: HTTPConnectionPool(host='localhost', port=53085): Max retries exceeded with url: /session/97dde21aed484580639fc95ad0b2aa98/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B355F100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-16 10:55:31,386 - ERROR - 在 3 次尝试后仍未获取到token
2025-07-16 10:55:31,386 - ERROR - ❌ 保号失败
2025-07-16 10:55:35,456 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B35F55B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/97dde21aed484580639fc95ad0b2aa98
2025-07-16 10:55:39,546 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B361CAF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/97dde21aed484580639fc95ad0b2aa98
2025-07-16 10:55:43,650 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000134B361CE20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/97dde21aed484580639fc95ad0b2aa98
2025-07-16 10:55:47,750 - INFO - 结果已保存到: single_bh_result.json
2025-07-16 10:55:47,751 - INFO - 
==================================================
2025-07-16 10:55:47,751 - INFO - 单个保号任务完成!
2025-07-16 10:55:47,752 - INFO - 状态: 失败
2025-07-16 10:55:47,752 - INFO - 错误: 无法获取新token
2025-07-16 10:55:47,752 - INFO - ❌ 保号失败
2025-07-16 10:55:47,752 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 10:55:47,752 - INFO - 日志文件: single_bh.log
2025-07-16 10:59:45,056 - INFO - 开始单个保号任务 - 2025-07-16 10:59:45
2025-07-16 10:59:45,056 - INFO - 成功加载配置文件: config.json
2025-07-16 10:59:45,057 - INFO - 从配置文件读取到session_token: user_01JWAWK66VXWVAB9D81BW70NEC%3A%3AeyJhbGciOiJIU...
2025-07-16 10:59:45,057 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 10:59:47,128 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 10:59:47,128 - INFO - 浏览器初始化成功
2025-07-16 10:59:47,129 - INFO - 开始获取会话令牌
2025-07-16 10:59:47,129 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 10:59:58,232 - INFO - 设置浏览器cookies
2025-07-16 10:59:58,735 - INFO - cookies设置完成
2025-07-16 10:59:58,736 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=OBTNDjp0NyRnostsNt6xAZolCLmbUOA6ohJJlh6elGw&uuid=c2a0ca21-a595-4af9-9432-0c8703375198&mode=login
2025-07-16 11:00:00,381 - INFO - 点击确认登录按钮
2025-07-16 11:00:01,928 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=c2a0ca21-a595-4af9-9432-0c8703375198&verifier=a9bNY7BGrWsK-4T70F905OwqeO3Lq4Odb9DenzRDZ3bXHdbTwX2XM5yEEQ
2025-07-16 11:00:02,815 - INFO - 成功获取账号token和userId
2025-07-16 11:00:02,816 - INFO - ✅ 保号成功!
2025-07-16 11:00:02,817 - INFO - 新 userId: user_01JWAWK66VXWVAB9D81BW70NEC
2025-07-16 11:00:02,817 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:00:06,049 - INFO - 结果已保存到: single_bh_result.json
2025-07-16 11:00:06,049 - INFO - 
==================================================
2025-07-16 11:00:06,050 - INFO - 单个保号任务完成!
2025-07-16 11:00:06,050 - INFO - 状态: 成功
2025-07-16 11:00:06,050 - INFO - 新 userId: user_01JWAWK66VXWVAB9D81BW70NEC
2025-07-16 11:00:06,050 - INFO - ✅ 保号成功
2025-07-16 11:00:06,050 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:00:06,051 - INFO - 日志文件: single_bh.log
2025-07-16 11:01:35,866 - INFO - 开始单个保号任务 - 2025-07-16 11:01:35
2025-07-16 11:01:35,866 - INFO - 成功加载配置文件: config.json
2025-07-16 11:01:35,867 - INFO - 从配置文件读取到session_token: user_01JWAXAZKG50C1PVEB6V4NGN63%3A%3AeyJhbGciOiJIU...
2025-07-16 11:01:35,867 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:01:37,809 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:01:37,809 - INFO - 浏览器初始化成功
2025-07-16 11:01:37,810 - INFO - 开始获取会话令牌
2025-07-16 11:01:37,810 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:01:48,911 - INFO - 设置浏览器cookies
2025-07-16 11:01:49,313 - INFO - cookies设置完成
2025-07-16 11:01:49,315 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=ofuuT-wUAaH1mraB3Wism6OdIVtrjmV_nXWZDAkUXcw&uuid=4e017f6f-8fdc-47a7-8e42-2fde09d5adf5&mode=login
2025-07-16 11:01:51,122 - INFO - 点击确认登录按钮
2025-07-16 11:01:52,663 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=4e017f6f-8fdc-47a7-8e42-2fde09d5adf5&verifier=S_Rlh_m03g6Ldwgr25QvjzJCfzT-aCfoEUCmaMDzelcTIzx2KUKNtXAuvQ
2025-07-16 11:01:53,874 - INFO - 成功获取账号token和userId
2025-07-16 11:01:53,876 - INFO - ✅ 保号成功!
2025-07-16 11:01:53,876 - INFO - 新 userId: user_01JWAXAZKG50C1PVEB6V4NGN63
2025-07-16 11:01:53,877 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:01:57,114 - INFO - 结果已保存到: single_bh_result.json
2025-07-16 11:01:57,115 - INFO - 
==================================================
2025-07-16 11:01:57,115 - INFO - 单个保号任务完成!
2025-07-16 11:01:57,116 - INFO - 状态: 成功
2025-07-16 11:01:57,116 - INFO - 新 userId: user_01JWAXAZKG50C1PVEB6V4NGN63
2025-07-16 11:01:57,116 - INFO - ✅ 保号成功
2025-07-16 11:01:57,116 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:01:57,116 - INFO - 日志文件: single_bh.log
2025-07-16 11:03:14,899 - INFO - 开始单个保号任务 - 2025-07-16 11:03:14
2025-07-16 11:03:14,900 - INFO - 成功加载配置文件: config.json
2025-07-16 11:03:14,900 - INFO - 从配置文件读取到session_token: user_01JWAXAZKG50C1PVEB6V4NGN63%3A%3AeyJhbGciOiJIU...
2025-07-16 11:03:14,900 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:04:11,091 - INFO - 开始单个保号任务 - 2025-07-16 11:04:11
2025-07-16 11:04:11,092 - INFO - 成功加载配置文件: config.json
2025-07-16 11:04:11,092 - INFO - 从配置文件读取到session_token: user_01JWAY0HSSQBRPRWDE5AG9H47E%3A%3AeyJhbGciOiJIU...
2025-07-16 11:04:11,092 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:04:13,047 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:04:13,047 - INFO - 浏览器初始化成功
2025-07-16 11:04:13,048 - INFO - 开始获取会话令牌
2025-07-16 11:04:13,048 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:04:24,501 - INFO - 设置浏览器cookies
2025-07-16 11:04:24,958 - INFO - cookies设置完成
2025-07-16 11:04:24,959 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=1nI9KaXJvIxO1whYfm_zFy1sEEwx3Tf2r_0tHoPhR_Y&uuid=da29c80d-7a06-4dc1-99bc-738363352dca&mode=login
2025-07-16 11:04:26,438 - INFO - 点击确认登录按钮
2025-07-16 11:04:27,978 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=da29c80d-7a06-4dc1-99bc-738363352dca&verifier=9_wo0c27EJ19WzEoQ1HxKJn6VwfzTeq9w36O_nr3RFF-bQo57RxLp793-w
2025-07-16 11:04:29,074 - INFO - 成功获取账号token和userId
2025-07-16 11:04:29,075 - INFO - ✅ 保号成功!
2025-07-16 11:04:29,076 - INFO - 新 userId: user_01JWAY0HSSQBRPRWDE5AG9H47E
2025-07-16 11:04:29,076 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:04:32,322 - INFO - 结果已追加保存到: single_bh_result.json (共 2 条记录)
2025-07-16 11:04:32,322 - INFO - 
==================================================
2025-07-16 11:04:32,323 - INFO - 单个保号任务完成!
2025-07-16 11:04:32,323 - INFO - 状态: 成功
2025-07-16 11:04:32,323 - INFO - 新 userId: user_01JWAY0HSSQBRPRWDE5AG9H47E
2025-07-16 11:04:32,323 - INFO - ✅ 保号成功
2025-07-16 11:04:32,323 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:04:32,323 - INFO - 日志文件: single_bh.log
2025-07-16 11:05:22,266 - INFO - 开始单个保号任务 - 2025-07-16 11:05:22
2025-07-16 11:05:22,267 - INFO - 成功加载配置文件: config.json
2025-07-16 11:05:22,267 - INFO - 从配置文件读取到session_token: user_01JWAZ4B6EYNEF0FD3NBNRKTVF%3A%3AeyJhbGciOiJIU...
2025-07-16 11:05:22,267 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:05:24,279 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:05:24,280 - INFO - 浏览器初始化成功
2025-07-16 11:05:24,280 - INFO - 开始获取会话令牌
2025-07-16 11:05:24,280 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:05:32,983 - INFO - 设置浏览器cookies
2025-07-16 11:05:33,307 - INFO - cookies设置完成
2025-07-16 11:05:33,308 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=NKHfD_-r9stlPFAQqHrCMkl6Le9BNaWJ73WAQWmz4TQ&uuid=4b5ae67d-40ef-4f5e-a97f-a8638bf32428&mode=login
2025-07-16 11:05:34,827 - INFO - 点击确认登录按钮
2025-07-16 11:05:36,368 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=4b5ae67d-40ef-4f5e-a97f-a8638bf32428&verifier=KWV0r_KhuPDmpc2UZ-055MjU4EDdU8QtyIBtW-ssKnW7hFRyLRGYiVkYsg
2025-07-16 11:05:37,664 - INFO - 成功获取账号token和userId
2025-07-16 11:05:37,666 - INFO - ✅ 保号成功!
2025-07-16 11:05:37,666 - INFO - 新 userId: user_01JWAZ4B6EYNEF0FD3NBNRKTVF
2025-07-16 11:05:37,667 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:05:40,913 - INFO - 结果已追加保存到: single_bh_result.json (共 3 条记录)
2025-07-16 11:05:40,914 - INFO - 
==================================================
2025-07-16 11:05:40,914 - INFO - 单个保号任务完成!
2025-07-16 11:05:40,914 - INFO - 状态: 成功
2025-07-16 11:05:40,915 - INFO - 新 userId: user_01JWAZ4B6EYNEF0FD3NBNRKTVF
2025-07-16 11:05:40,915 - INFO - ✅ 保号成功
2025-07-16 11:05:40,915 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:05:40,915 - INFO - 日志文件: single_bh.log
2025-07-16 11:07:00,007 - INFO - 开始单个保号任务 - 2025-07-16 11:07:00
2025-07-16 11:07:00,008 - INFO - 成功加载配置文件: config.json
2025-07-16 11:07:00,008 - INFO - 从配置文件读取到session_token: user_01JWB33TPSNVPDGGVGGCS5FDQR%3A%3AeyJhbGciOiJIU...
2025-07-16 11:07:00,008 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:07:02,035 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:07:02,036 - INFO - 浏览器初始化成功
2025-07-16 11:07:02,036 - INFO - 开始获取会话令牌
2025-07-16 11:07:02,036 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:07:13,087 - INFO - 设置浏览器cookies
2025-07-16 11:07:13,537 - INFO - cookies设置完成
2025-07-16 11:07:13,539 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=w6YcgOLJ_Pp4rukwMkTwGXtwX_iBMc3DjpQbanU8a-w&uuid=8a874e03-c2e8-4a73-aa9b-bcbd5351e0c6&mode=login
2025-07-16 11:07:15,043 - INFO - 点击确认登录按钮
2025-07-16 11:07:16,592 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=8a874e03-c2e8-4a73-aa9b-bcbd5351e0c6&verifier=P2ZwepzLdT48uATTJjP1_WJ3g60NDPdLIoY8ootU_aBLwvrqHAhLS8GJjA
2025-07-16 11:07:17,437 - INFO - 成功获取账号token和userId
2025-07-16 11:07:17,439 - INFO - ✅ 保号成功!
2025-07-16 11:07:17,439 - INFO - 新 userId: user_01JWB33TPSNVPDGGVGGCS5FDQR
2025-07-16 11:07:17,439 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:07:20,661 - INFO - 结果已追加保存到: single_bh_result.json (共 4 条记录)
2025-07-16 11:07:20,661 - INFO - 
==================================================
2025-07-16 11:07:20,662 - INFO - 单个保号任务完成!
2025-07-16 11:07:20,662 - INFO - 状态: 成功
2025-07-16 11:07:20,662 - INFO - 新 userId: user_01JWB33TPSNVPDGGVGGCS5FDQR
2025-07-16 11:07:20,663 - INFO - ✅ 保号成功
2025-07-16 11:07:20,663 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:07:20,663 - INFO - 日志文件: single_bh.log
2025-07-16 11:08:03,933 - INFO - 开始单个保号任务 - 2025-07-16 11:08:03
2025-07-16 11:08:03,934 - INFO - 成功加载配置文件: config.json
2025-07-16 11:08:03,934 - INFO - 从配置文件读取到session_token: user_01JWB4395X0WYZF264ZDYX5X13%3A%3AeyJhbGciOiJIU...
2025-07-16 11:08:03,934 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:08:05,890 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:08:05,891 - INFO - 浏览器初始化成功
2025-07-16 11:08:05,891 - INFO - 开始获取会话令牌
2025-07-16 11:08:05,891 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:08:16,781 - INFO - 设置浏览器cookies
2025-07-16 11:08:17,186 - INFO - cookies设置完成
2025-07-16 11:08:17,186 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=2uWHkwsTZ24CfhQG37fRCDngvIaXFieXj3olnG4_-zw&uuid=27f29e62-adfb-4247-9c53-c5d27516baf0&mode=login
2025-07-16 11:08:18,951 - INFO - 点击确认登录按钮
2025-07-16 11:08:20,501 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=27f29e62-adfb-4247-9c53-c5d27516baf0&verifier=hapH7_oKK8KaFU91YzG0bjLogpSWCMhyT41zYwF5ssZIxSj5IiMdpAfuwQ
2025-07-16 11:08:21,341 - INFO - 成功获取账号token和userId
2025-07-16 11:08:21,342 - INFO - ✅ 保号成功!
2025-07-16 11:08:21,342 - INFO - 新 userId: user_01JWB4395X0WYZF264ZDYX5X13
2025-07-16 11:08:21,342 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:08:24,564 - INFO - 结果已追加保存到: single_bh_result.json (共 5 条记录)
2025-07-16 11:08:24,565 - INFO - 
==================================================
2025-07-16 11:08:24,565 - INFO - 单个保号任务完成!
2025-07-16 11:08:24,565 - INFO - 状态: 成功
2025-07-16 11:08:24,565 - INFO - 新 userId: user_01JWB4395X0WYZF264ZDYX5X13
2025-07-16 11:08:24,565 - INFO - ✅ 保号成功
2025-07-16 11:08:24,565 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:08:24,566 - INFO - 日志文件: single_bh.log
2025-07-16 11:09:11,619 - INFO - 开始单个保号任务 - 2025-07-16 11:09:11
2025-07-16 11:09:11,620 - INFO - 成功加载配置文件: config.json
2025-07-16 11:09:11,620 - INFO - 从配置文件读取到session_token: user_01JWB7DMDB6Y8Z8QPC4W4MEAZ6%3A%3AeyJhbGciOiJIU...
2025-07-16 11:09:11,620 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:09:13,589 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:09:13,589 - INFO - 浏览器初始化成功
2025-07-16 11:09:13,589 - INFO - 开始获取会话令牌
2025-07-16 11:09:13,590 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:09:25,397 - INFO - 设置浏览器cookies
2025-07-16 11:09:25,618 - INFO - cookies设置完成
2025-07-16 11:09:25,620 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=JXu5lJworsV8lIfQkyJIdTtz_HPf8MAliCC-r0mjFIQ&uuid=9321e41f-fff9-4fe6-9060-35acfb4dd68b&mode=login
2025-07-16 11:09:27,539 - INFO - 点击确认登录按钮
2025-07-16 11:09:29,081 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=9321e41f-fff9-4fe6-9060-35acfb4dd68b&verifier=9wcysud9b8tOwmh0Ktvh0pdFLvPMonb7Eq3m3ClnjCarvO1ZayjqHUo0kg
2025-07-16 11:09:29,991 - INFO - 成功获取账号token和userId
2025-07-16 11:09:29,993 - INFO - ✅ 保号成功!
2025-07-16 11:09:29,993 - INFO - 新 userId: user_01JWB7DMDB6Y8Z8QPC4W4MEAZ6
2025-07-16 11:09:29,993 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:09:33,253 - INFO - 结果已追加保存到: single_bh_result.json (共 6 条记录)
2025-07-16 11:09:33,253 - INFO - 
==================================================
2025-07-16 11:09:33,253 - INFO - 单个保号任务完成!
2025-07-16 11:09:33,254 - INFO - 状态: 成功
2025-07-16 11:09:33,254 - INFO - 新 userId: user_01JWB7DMDB6Y8Z8QPC4W4MEAZ6
2025-07-16 11:09:33,254 - INFO - ✅ 保号成功
2025-07-16 11:09:33,254 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:09:33,254 - INFO - 日志文件: single_bh.log
2025-07-16 11:10:58,843 - INFO - 开始单个保号任务 - 2025-07-16 11:10:58
2025-07-16 11:10:58,843 - INFO - 成功加载配置文件: config.json
2025-07-16 11:10:58,843 - INFO - 从配置文件读取到session_token: user_01JWAWK66VXWVAB9D81BW70NEC%3A%3AeyJhbGciOiJIU...
2025-07-16 11:10:58,844 - INFO - 使用Chrome路径: C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe
2025-07-16 11:11:00,797 - INFO - ✅ 使用指定ChromeDriver路径成功创建WebDriver
2025-07-16 11:11:00,798 - INFO - 浏览器初始化成功
2025-07-16 11:11:00,798 - INFO - 开始获取会话令牌
2025-07-16 11:11:00,798 - INFO - 首先访问网站: https://www.cursor.com/
2025-07-16 11:11:10,953 - INFO - 设置浏览器cookies
2025-07-16 11:11:11,314 - INFO - cookies设置完成
2025-07-16 11:11:11,315 - INFO - 访问深度登录URL: https://www.cursor.com/cn/loginDeepControl?challenge=4W-Q09i346IzS6GGDGK57lZQWxmpCzew1Wldai8921M&uuid=151294b5-9cb1-4e73-85b5-b669bb24c26a&mode=login
2025-07-16 11:11:13,826 - INFO - 点击确认登录按钮
2025-07-16 11:11:15,374 - INFO - 轮询认证状态: https://api2.cursor.sh/auth/poll?uuid=151294b5-9cb1-4e73-85b5-b669bb24c26a&verifier=W3VD-yzzvs4Iid8q7JsroEkLvfDQTJ6fFxBS0CYI469aqX9J9eForLYpGQ
2025-07-16 11:11:16,234 - INFO - 成功获取账号token和userId
2025-07-16 11:11:16,236 - INFO - ✅ 保号成功!
2025-07-16 11:11:16,236 - INFO - 新 userId: user_01JWAWK66VXWVAB9D81BW70NEC
2025-07-16 11:11:16,236 - INFO - 新 accessToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhd...
2025-07-16 11:11:19,470 - INFO - 结果已追加保存到: single_bh_result.json (共 7 条记录)
2025-07-16 11:11:19,470 - INFO - 
==================================================
2025-07-16 11:11:19,470 - INFO - 单个保号任务完成!
2025-07-16 11:11:19,470 - INFO - 状态: 成功
2025-07-16 11:11:19,471 - INFO - 新 userId: user_01JWAWK66VXWVAB9D81BW70NEC
2025-07-16 11:11:19,471 - INFO - ✅ 保号成功
2025-07-16 11:11:19,471 - INFO - 详细结果已保存到: single_bh_result.json
2025-07-16 11:11:19,471 - INFO - 日志文件: single_bh.log
